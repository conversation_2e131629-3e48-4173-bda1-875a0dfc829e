# GS Chat Maestro Frontend

A React-based frontend application for the Chat Translator project that integrates with Supabase authentication and provides task management functionality.

## Project Structure

```
src/
├── assets/         # Static assets and images
├── components/     # Reusable React components
│   ├── auth/      # Authentication related components
│   ├── chats/     # Chat interface components
│   ├── magicui/   # UI components library
│   └── ui/        # Custom UI components
├── contexts/      # React context providers
├── hooks/         # Custom React hooks
├── lib/           # Utility functions and configurations
├── mutations/     # API mutation handlers
├── pages/         # Page components
└── App.tsx        # Main application component
```

## Authentication

The application uses Supabase authentication from the "Chat Translator" project. Authentication state is managed through the Supabase client and integrated with the application's context system.

## API Endpoints

### Tasks Endpoint
- **Purpose**: Manage tasks with SiteInfo structure
- **Structure**:
  ```typescript
  interface SiteInfo {

  }
  ```

### Images Endpoint
- **Purpose**: Fetch images based on site-specific requirements
- **Functionality**: Returns images relevant to the specified site

## Development Setup

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```
3. Configure environment variables:
   - Create a `.env` file with Supabase credentials
   - Add other necessary environment variables

4. Start the development server:
   ```bash
   npm run dev
   ```

## Pending Tasks

1. intergrate APIs
