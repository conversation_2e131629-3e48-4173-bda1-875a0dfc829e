import { Dock, DockIcon } from '@/components/magicui/dock';
import { HomeIcon, LogOutIcon, UserIcon } from 'lucide-react';
import { useAuth } from '@/contexts/auth-context';
import { useCallback, useEffect } from 'react';
import { ThemeSwitcher } from '@/components/theme-switcher';
import DashboardSkeleton from '@/components/dashboard/dashboard-skelton';
import MobileLayout from '@/components/dashboard/mobile-layout';
import DesktopLayout from '@/components/dashboard/desktop-layout';
import { useLanguageStore } from '@/stores/language-store';
import { useTaskStore } from '@/stores/task-store';
import TaskExpiredDialog from '@/components/task-expired-dialog';

export default function Dashboard() {
  const { logout, user } = useAuth();
  const { language } = useLanguageStore();
  const POLL_INTERVAL = 5;

  // Task store
  const {
    task,
    isTaskExpired,
    shouldPoll,
    isLoading,
    setTaskExpired,
    setShouldPoll,
    clearTask,
    fetchTask,
    submitTask,
    checkAndHandleExpiration
  } = useTaskStore();

  // Handle task submission
  const handleSubmitTask = useCallback(async (response: string) => {
    if (!task) return;

    try {
      console.log('🚀 Submitting task...');
      await submitTask(response, language);
      console.log('✅ Task submitted successfully, polling should start immediately');
      // Note: fetchTask will be called immediately by the polling effect
      // since submitTask sets shouldPoll to true and clears the task
    } catch (error) {
      console.error('Task submission error:', error);
    }
  }, [task, submitTask, language]);

  // Handle task expiration
  const handleTaskExpired = useCallback(() => {
    setTaskExpired(true);
    setShouldPoll(false);
  }, [setTaskExpired, setShouldPoll]);

  // Handle fetching new task after expiration
  const handleFetchNewTask = useCallback(() => {
    // Clear the old task data first (as per user preference to explicitly set to null)
    clearTask();
    setTaskExpired(false);
    setShouldPoll(true);
    fetchTask(user?.email);
  }, [clearTask, setTaskExpired, setShouldPoll, fetchTask, user?.email]);
  

  // Immediate task fetching when shouldPoll becomes true
  useEffect(() => {
    if (!task && !isLoading && shouldPoll) {
      console.log('🔄 Immediate fetch triggered - task:', !!task, 'loading:', isLoading, 'shouldPoll:', shouldPoll);
      fetchTask(user?.email);
    }
  }, [task, isLoading, shouldPoll, fetchTask, user?.email]);

  // Background polling when no tasks are available
  useEffect(() => {
    if (!task && !isLoading && shouldPoll) {
      console.log(`⏰ Background polling scheduled in ${POLL_INTERVAL} seconds`);
      const timer = setTimeout(() => {
        console.log('🔄 Background polling fetch triggered');
        fetchTask(user?.email);
      }, POLL_INTERVAL * 1000);

      return () => clearTimeout(timer);
    }
  }, [POLL_INTERVAL, task, isLoading, fetchTask, shouldPoll, user?.email]);

  // Check for task expiration on mount and when task changes
  useEffect(() => {
    if (task) {
      checkAndHandleExpiration();
    }
  }, [task, checkAndHandleExpiration]);

  // If loading, show skeleton loader
  if (isLoading || !task) {
    return (
      <DashboardSkeleton />
    );
  }
  
  return (
    <div className="bg-[#330022] min-h-screen dark:bg-[#17000f]">
      <Dock
        direction="middle"
        className="absolute -top-4 left-1/2 -translate-x-1/2 z-10 h-12 bg-white md:h-12"
        iconMagnification={60}
        iconDistance={100}
      >
        <DockIcon>
          <HomeIcon className="w-4 h-4 md:w-5 md:h-5" />
        </DockIcon>
        <DockIcon>
          <UserIcon className="w-4 h-4 md:w-5 md:h-5" />
        </DockIcon>
        <DockIcon>
          <ThemeSwitcher />
        </DockIcon>
        <DockIcon onClick={logout}>
          <LogOutIcon className="w-4 h-4 md:w-5 md:h-5" />
        </DockIcon>
      </Dock>

      <MobileLayout
        onSubmit={handleSubmitTask}
        onTaskExpired={handleTaskExpired}
      />

      <DesktopLayout
        onSubmit={handleSubmitTask}
        onTaskExpired={handleTaskExpired}
      />

      <TaskExpiredDialog
        isOpen={isTaskExpired}
        onFetchNewTask={handleFetchNewTask}
      />
    </div>
  );
}

