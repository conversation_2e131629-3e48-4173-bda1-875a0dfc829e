import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import './index.css';
import App from './App.tsx';
import { BrowserRouter } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { AuthProvider } from './contexts/auth-context.tsx';
import { Toaster } from './components/ui/sonner';
import { ThemeProvider } from './components/theme-provider.tsx';
import LanguageInitializer from './components/language-initializer.tsx';
const queryClient = new QueryClient();

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <QueryClientProvider client={queryClient}>
      <ThemeProvider>
        <LanguageInitializer>
          <BrowserRouter>
            <AuthProvider>
              <App />
              <Toaster />
            </AuthProvider>
          </BrowserRouter>
        </LanguageInitializer>
      </ThemeProvider>
    </QueryClientProvider>
  </StrictMode>
);
