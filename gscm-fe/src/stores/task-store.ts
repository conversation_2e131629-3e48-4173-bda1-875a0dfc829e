import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { Task, TaskSubmission } from '@/mutations/task-mutations';
import apiClient from '@/lib/api-client';
import { toast } from 'sonner';

interface TaskState {
  // Task data
  task: Task | null;
  
  // Polling and expiration state
  isTaskExpired: boolean;
  shouldPoll: boolean;
  isLoading: boolean;
  isSubmitting: boolean;
  
  // Actions
  setTask: (task: Task | null) => void;
  clearTask: () => void;
  setTaskExpired: (expired: boolean) => void;
  setShouldPoll: (shouldPoll: boolean) => void;
  setIsLoading: (loading: boolean) => void;
  setIsSubmitting: (submitting: boolean) => void;
  
  // Task operations
  fetchTask: () => Promise<Task | null>;
  submitTask: (response: string, language: string) => Promise<void>;
  
  // Utility methods
  isTaskValid: () => boolean;
  getTimeUntilExpiration: () => number;
  checkAndHandleExpiration: () => boolean;
}

export const useTaskStore = create<TaskState>()(
  persist(
    (set, get) => ({
      // Initial state
      task: null,
      isTaskExpired: false,
      shouldPoll: true,
      isLoading: false,
      isSubmitting: false,

      // Basic setters
      setTask: (task: Task | null) => {
        set({ task });
        
        // Reset expiration state when new task is set
        if (task) {
          set({ isTaskExpired: false });
        }
      },

      clearTask: () => {
        console.log('🧹 clearTask called - setting task to null and shouldPoll to true');
        set({
          task: null,
          isTaskExpired: false,
          shouldPoll: true
        });
      },

      setTaskExpired: (expired: boolean) => {
        set({ isTaskExpired: expired });
        
        // Stop polling when task expires
        if (expired) {
          set({ shouldPoll: false });
        }
      },

      setShouldPoll: (shouldPoll: boolean) => {
        set({ shouldPoll });
      },

      setIsLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },

      setIsSubmitting: (submitting: boolean) => {
        set({ isSubmitting: submitting });
      },

      // Fetch task from API
      fetchTask: async (): Promise<Task | null> => {
        const { setIsLoading, setTask } = get();
        
        try {
          setIsLoading(true);

          // const task = await apiClient.task.get<Task>("/task", { skipErrorToast: true });
          // Use the existing data.json for now (as per current implementation)
          const task = await import('@/data.json').then(module => module.default as unknown as Task);

          console.log('Fetched task:', task);
          setTask(task);
          return task;
          
        } catch (error: unknown) {
          console.error('Failed to fetch task:', error);
          
          // Handle "No tasks available" error
          if (
            error &&
            typeof error === 'object' &&
            'response' in error &&
            error.response &&
            typeof error.response === 'object' &&
            'status' in error.response &&
            error.response.status === 404
          ) {
            setTask(null);
            return null;
          }
          
          throw error;
        } finally {
          setIsLoading(false);
        }
      },

      // Submit task
      submitTask: async (response: string, language: string): Promise<void> => {
        const { task, setIsSubmitting, clearTask, setShouldPoll } = get();
        
        if (!task) {
          throw new Error('No task available to submit');
        }

        try {
          setIsSubmitting(true);

          const submission: TaskSubmission = {
            taskId: task.taskId,
            expiresAt: task.expiresAt,
            data: {
              ...task.data,
              resText: response,
              language: language
            }
          };

          // await apiClient.task.post("/task/submit", submission);
          // Mock the submission for testing - simulate a successful submission
          console.log('📤 Mock submission:', submission);
          await new Promise(resolve => setTimeout(resolve, 500)); // Simulate network delay
          
          toast.success('Task submitted successfully');

          // Clear task and start polling after successful submission
          console.log('🧹 Clearing task and enabling polling after submission');
          clearTask();
          setShouldPoll(true);
          
        } catch (error) {
          toast.error('Failed to submit task');
          console.error('Task submission error:', error);
          throw error;
        } finally {
          setIsSubmitting(false);
        }
      },

      // Check if current task is valid (exists and not expired)
      isTaskValid: (): boolean => {
        const { task, isTaskExpired } = get();
        return task !== null && !isTaskExpired;
      },

      // Get time until expiration in seconds
      getTimeUntilExpiration: (): number => {
        const { task } = get();
        
        if (!task?.expiresAt) {
          return 0;
        }

        const expiry = new Date(task.expiresAt);
        const now = new Date();
        return Math.max(0, Math.floor((expiry.getTime() - now.getTime()) / 1000));
      },

      // Check if task is expired and handle expiration
      checkAndHandleExpiration: (): boolean => {
        const { task, setTaskExpired, setShouldPoll } = get();
        
        if (!task) {
          return false;
        }

        const timeLeft = get().getTimeUntilExpiration();
        
        if (timeLeft <= 0) {
          setTaskExpired(true);
          setShouldPoll(false);
          return true;
        }
        
        return false;
      }
    }),
    {
      name: 'task-storage',
      version: 1,
      // Only persist the task data, not the transient state
      partialize: (state) => ({
        task: state.task,
        isTaskExpired: state.isTaskExpired
      }),
      // Handle rehydration - check if persisted task is expired
      onRehydrateStorage: () => (state) => {
        if (!state) return;

        if (state.task) {
          // Check if persisted task is expired
          const expiry = new Date(state.task.expiresAt);
          const now = new Date();
          const isExpired = now >= expiry;

          if (isExpired) {
            // If persisted task is expired, trigger polling
            state.isTaskExpired = true;
            state.shouldPoll = true;
          } else {
            // If persisted task is valid, allow editing
            state.isTaskExpired = false;
            state.shouldPoll = false;
          }
        } else {
          // No persisted task, start polling
          state.shouldPoll = true;
        }
      }
    }
  )
);
