import { create } from 'zustand';
import { persist } from 'zustand/middleware';

// Interface for task completion data
export interface TaskCompletion {
  taskId: string;
  userEmail: string;
  timeProvided: number; // seconds - total time allocated for the task
  timeTaken: number; // seconds - actual time taken by user
  response: string; // user's response/submission
  completedAt: string; // ISO timestamp of completion
}

// Interface for current timing session
interface TimingSession {
  taskId: string;
  startTime: number; // timestamp when task started
  expirationTime: number; // timestamp when task expires
  userEmail: string;
}

interface TimeTrackingState {
  // Current timing session
  currentSession: TimingSession | null;
  
  // Historical completion data (persisted)
  completions: TaskCompletion[];
  
  // Actions
  startTiming: (taskId: string, expiresAt: string, userEmail: string) => void;
  stopTiming: () => void;
  recordCompletion: (response: string) => TaskCompletion | null;
  getCompletions: () => TaskCompletion[];
  getCompletionsByUser: (userEmail: string) => TaskCompletion[];
  clearCompletions: () => void;
  
  // Utility methods
  getCurrentTimeProvided: () => number;
  getCurrentTimeTaken: () => number;
  isTimingActive: () => boolean;
}

export const useTimeTrackingStore = create<TimeTrackingState>()(
  persist(
    (set, get) => ({
      // Initial state
      currentSession: null,
      completions: [],

      // Start timing for a new task
      startTiming: (taskId: string, expiresAt: string, userEmail: string) => {
        const now = Date.now();
        const expirationTime = new Date(expiresAt).getTime();
        
        console.log('⏱️ Starting timing for task:', taskId);
        console.log('⏱️ Task expires at:', expiresAt);
        console.log('⏱️ Time provided:', Math.floor((expirationTime - now) / 1000), 'seconds');
        
        set({
          currentSession: {
            taskId,
            startTime: now,
            expirationTime,
            userEmail
          }
        });
      },

      // Stop timing (without recording completion)
      stopTiming: () => {
        console.log('⏱️ Stopping timing session');
        set({ currentSession: null });
      },

      // Record task completion and calculate times
      recordCompletion: (response: string): TaskCompletion | null => {
        const { currentSession, completions } = get();
        
        if (!currentSession) {
          console.warn('⏱️ No active timing session to record completion');
          return null;
        }

        const completionTime = Date.now();
        const timeTaken = Math.floor((completionTime - currentSession.startTime) / 1000);
        const timeProvided = Math.floor((currentSession.expirationTime - currentSession.startTime) / 1000);

        const completion: TaskCompletion = {
          taskId: currentSession.taskId,
          userEmail: currentSession.userEmail,
          timeProvided,
          timeTaken,
          response,
          completedAt: new Date(completionTime).toISOString()
        };

        console.log('⏱️ Recording task completion:', {
          taskId: completion.taskId,
          timeProvided: completion.timeProvided,
          timeTaken: completion.timeTaken,
          efficiency: `${Math.round((completion.timeTaken / completion.timeProvided) * 100)}%`
        });

        // Add to completions and clear current session
        set({
          completions: [...completions, completion],
          currentSession: null
        });

        return completion;
      },

      // Get all completions
      getCompletions: () => {
        return get().completions;
      },

      // Get completions for a specific user
      getCompletionsByUser: (userEmail: string) => {
        return get().completions.filter(completion => completion.userEmail === userEmail);
      },

      // Clear all completion history
      clearCompletions: () => {
        console.log('⏱️ Clearing all completion history');
        set({ completions: [] });
      },

      // Get time provided for current task (in seconds)
      getCurrentTimeProvided: (): number => {
        const { currentSession } = get();
        if (!currentSession) return 0;
        
        return Math.floor((currentSession.expirationTime - currentSession.startTime) / 1000);
      },

      // Get time taken so far for current task (in seconds)
      getCurrentTimeTaken: (): number => {
        const { currentSession } = get();
        if (!currentSession) return 0;
        
        return Math.floor((Date.now() - currentSession.startTime) / 1000);
      },

      // Check if timing is currently active
      isTimingActive: (): boolean => {
        return get().currentSession !== null;
      }
    }),
    {
      name: 'time-tracking-storage',
      version: 1,
      // Only persist completions, not current session
      partialize: (state) => ({
        completions: state.completions
      }),
      // Handle rehydration
      onRehydrateStorage: () => (state) => {
        if (!state) return;
        
        // Ensure current session is cleared on rehydration
        // (timing sessions should not persist across app restarts)
        state.currentSession = null;
        
        console.log('⏱️ Rehydrated time tracking store with', state.completions.length, 'completions');
      }
    }
  )
);

// Helper function to format time duration
export const formatDuration = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  
  if (minutes > 0) {
    return `${minutes}m ${remainingSeconds}s`;
  }
  return `${remainingSeconds}s`;
};

// Helper function to calculate efficiency percentage
export const calculateEfficiency = (timeTaken: number, timeProvided: number): number => {
  if (timeProvided === 0) return 0;
  return Math.round((timeTaken / timeProvided) * 100);
};
