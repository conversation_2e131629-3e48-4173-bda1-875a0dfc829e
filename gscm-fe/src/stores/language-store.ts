import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { DeeplSupportedLanguage } from '@/lib/language-service';

interface LanguageState {
  language: DeeplSupportedLanguage;
  setLanguage: (language: DeeplSupportedLanguage) => void;
}

export const useLanguageStore = create<LanguageState>()(
  persist(
    (set) => ({
      language: DeeplSupportedLanguage.DE, // Default to German
      setLanguage: (language: DeeplSupportedLanguage) => {
        set({ language });
      },
    }),
    {
      name: 'language-storage', // localStorage key
      version: 1,
    }
  )
);