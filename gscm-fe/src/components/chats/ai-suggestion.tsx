import { Button } from '@/components/ui/button';
import { Edit, Check } from 'lucide-react';
import { useState } from 'react';

interface AiSuggestionProps {
  suggestion: string;
  onEdit: (text: string) => void;
  onAccept: () => void;
  disabled?: boolean;
}

export default function AiSuggestion({ 
  suggestion, 
  onEdit, 
  onAccept, 
  disabled = false 
}: AiSuggestionProps) {
  const [isVisible, setIsVisible] = useState(true);

  if (!suggestion || !isVisible) {
    return null;
  }

  const handleEdit = () => {
    onEdit(suggestion);
    setIsVisible(false);
  };

  const handleAccept = () => {
    onAccept();
    setIsVisible(false);
  };

  return (
    <div className="mx-4 mb-2 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg mt-3">
      <div className="flex items-start gap-3">
        <div className="flex-1">
          <div className="text-xs font-medium text-blue-700 dark:text-blue-300 mb-1">
            AI Suggestion ✨
          </div>
          <div className="text-sm text-gray-700 dark:text-gray-300 leading-relaxed">
            {suggestion}
          </div>
        </div>
        
        <div className="flex gap-2 flex-shrink-0">
          <Button
            size="sm"
            variant="outline"
            onClick={handleEdit}
            disabled={disabled}
            className="h-8 px-2 text-xs border-blue-300 dark:border-blue-700 hover:bg-blue-100 dark:hover:bg-blue-800/50"
          >
            <Edit className="h-3 w-3 mr-1" />
            Edit
          </Button>
          
          <Button
            size="sm"
            onClick={handleAccept}
            disabled={disabled}
            className="h-8 px-2 text-xs bg-[#c92cc4] hover:bg-[#9a1c96] dark:bg-[#9a1c96] dark:hover:bg-[#7a1676] text-white"
          >
            <Check className="h-3 w-3 mr-1" />
            Accept
          </Button>
        </div>
      </div>
    </div>
  );
}
