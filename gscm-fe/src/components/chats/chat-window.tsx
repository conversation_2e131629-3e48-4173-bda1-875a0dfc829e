import { formatDistanceToNow } from 'date-fns';
import { useState, useCallback, useRef } from 'react';
import MessageInput, { MessageInputRef } from './message-input';
import ChatBubble from './chat-bubble';
import ChatWindowHeader from './chat-window-header';
import SystemMessage from './system-message';
import AiSuggestion from './ai-suggestion';
import { CustomerInfo, Message as TaskMessage } from '@/mutations/task-mutations';
import { useTaskStore } from '@/stores/task-store';

export type Message = {
  text: string;
  type: 'received' | 'sent' | 'system';
  messageType: 'image' | 'text';
  timestamp: string;
  imageSrc?: string;
};

export type Moderator = {
  moderatorId: string;
  moderatorName: string;
  moderatorAge: number;
  moderatorAvatar: string;
};

interface ChatWindowProps {
  customer: CustomerInfo;
  messages: Message[];
  onSubmit?: (response: string) => void;
  onTaskExpired?: () => void;
}

export default function ChatWindow({
  customer,
  messages,
  onSubmit,
  onTaskExpired
}: ChatWindowProps) {
  const [isExpired, setIsExpired] = useState(false);
  const messageInputRef = useRef<MessageInputRef>(null);

  // Get task from store
  const { task } = useTaskStore();

  // Get profile pictures from task
  const customerProfilePic = task?.data.siteInfos.metaData.customerProfilePic;
  const moderatorProfilePic = task?.data.siteInfos.metaData.moderatorProfilePic;

  // Handle expiration change from header
  const handleExpirationChange = useCallback((expired: boolean) => {
    setIsExpired(expired);
  }, []);

  // Handle AI suggestion edit
  const handleAiSuggestionEdit = useCallback((text: string) => {
    messageInputRef.current?.setMessage(text);
  }, []);

  // Handle AI suggestion accept
  const handleAiSuggestionAccept = useCallback(() => {
    if (task?.data.resText && onSubmit && !isExpired) {
      onSubmit(task.data.resText);
    }
  }, [task, onSubmit, isExpired]);

  // Use task data if available, otherwise use props
  const displayMessages = task
    ? task.data.siteInfos.messages
        .map((m: TaskMessage, index: number) => ({
          id: m.id || index,
          text: m.text,
          type: m.type as 'received' | 'sent' | 'system',
          messageType: m.messageType,
          timestamp: m.timestamp,
          imageSrc: m.imageSrc
        }))
        .sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime())
    : messages.slice().sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());

  return (
    <div className="h-full flex flex-col">
      <ChatWindowHeader
        customer={customer}
        onTaskExpired={onTaskExpired}
        onExpirationChange={handleExpirationChange}
      />

      {task?.data.resText && (
        <AiSuggestion
          suggestion={task.data.resText}
          onEdit={handleAiSuggestionEdit}
          onAccept={handleAiSuggestionAccept}
          disabled={isExpired}
        />
      )}

      <div className="flex-1 overflow-hidden min-h-0 mt-8">
        <div className="h-full overflow-y-auto flex flex-col">
          {displayMessages?.map((message, index) =>
            message.type === 'system' ? (
              <SystemMessage
                key={index}
                message={message.text}
                timestamp={formatDistanceToNow(new Date(message.timestamp))}
              />
            ) : (
              <ChatBubble
                key={index}
                message={message.text}
                timestamp={formatDistanceToNow(new Date(message.timestamp))}
                isOutgoing={message.type === 'received'}
                avatarUrl={message.type === 'received' ? customerProfilePic : moderatorProfilePic}
                senderInitial={message.type === 'received' ? 'C' : 'M'}
                imageUrl={message.messageType === 'image' ? message.imageSrc : undefined}
              />
            )
          )}
        </div>
      </div>
      <div className="flex-shrink-0 mt-auto">
        <MessageInput
          ref={messageInputRef}
          onSubmit={(text) => {
            if (onSubmit && task && !isExpired) {
              onSubmit(text);
            }
          }}
          minLength={task?.data?.siteInfos?.metaData?.minLength || 40}
          disabled={isExpired}
        />
      </div>
    </div>
  );
}
