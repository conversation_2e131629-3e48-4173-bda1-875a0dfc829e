import { useState } from 'react';
import { Pop<PERSON>, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Input } from '@/components/ui/input';
import { Search, ImageIcon, X } from 'lucide-react';
import { Button } from '@/components/ui/button';

type StickerCategory = 'funny' | 'sad' | 'angry' | 'all';

interface Sticker {
  id: string;
  url: string;
  name: string;
  category: Exclude<StickerCategory, 'all'>;
}

// Mock stickers data
const stickers: Sticker[] = [
  { id: '1', url: 'https://mighty.tools/mockmind-api/content/human/125.jpg', name: 'Laughing Face', category: 'funny' },
  { id: '2', url: 'https://mighty.tools/mockmind-api/content/human/126.jpg', name: '<PERSON><PERSON>', category: 'funny' },
  { id: '3', url: 'https://mighty.tools/mockmind-api/content/human/127.jpg', name: 'Crying Face', category: 'sad' },
  { id: '4', url: 'https://mighty.tools/mockmind-api/content/human/128.jpg', name: 'Tears', category: 'sad' },
  { id: '5', url: 'https://mighty.tools/mockmind-api/content/human/129.jpg', name: 'Rage', category: 'angry' },
];

interface ImageSelectionProps {
  value?: Sticker | null;
  onChange?: (value: Sticker | null) => void;
}

export default function ImageSelection({ value, onChange }: ImageSelectionProps) {
  const [open, setOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  const handleStickerSelect = (sticker: Sticker) => {
    onChange?.(sticker);
    setOpen(false);
  };

  const handleClear = () => {
    onChange?.(null);
  };

  const filterStickers = (category: StickerCategory) => {
    const filteredByCategory =
      category === 'all' ? stickers : stickers.filter((sticker) => sticker.category === category);

    if (!searchQuery) return filteredByCategory;

    return filteredByCategory.filter((sticker) => sticker.name.toLowerCase().includes(searchQuery.toLowerCase()));
  };

  return (
    <div className="flex items-center gap-2">
      <Popover
        open={open}
        onOpenChange={setOpen}
      >
        <PopoverTrigger asChild disabled>
          <Button variant="ghost" size="icon" className="h-8 w-8">
            <ImageIcon className="h-4 w-4" />
          </Button>
        </PopoverTrigger>
        <PopoverContent
          className="w-80 p-0"
          align="start"
          side="top"
        >
          <Tabs
            defaultValue="all"
            className="w-full"
          >
            <div className="flex items-center px-4 pt-4 pb-2">
              <div className="relative flex-1">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search stickers..."
                  className="pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>
            <div className="px-4">
              <TabsList className="w-full grid grid-cols-4">
                <TabsTrigger value="all">All</TabsTrigger>
                <TabsTrigger value="funny">Funny</TabsTrigger>
                <TabsTrigger value="sad">Sad</TabsTrigger>
                <TabsTrigger value="angry">Angry</TabsTrigger>
              </TabsList>
            </div>
            {(['all', 'funny', 'sad', 'angry'] as const).map((category) => (
              <TabsContent
                key={category}
                value={category}
                className="mt-0"
              >
                <ScrollArea className="h-60">
                  <div className="grid grid-cols-4 gap-2 p-4">
                    {filterStickers(category).map((sticker) => (
                      <div
                        key={sticker.id}
                        className="p-2 rounded-md cursor-pointer hover:bg-accent transition-colors"
                        onClick={() => handleStickerSelect(sticker)}
                      >
                        <img
                          src={sticker.url}
                          alt={sticker.name}
                          className="w-full h-12 object-contain"
                          title={sticker.name}
                        />
                      </div>
                    ))}
                    {filterStickers(category).length === 0 && (
                      <div className="col-span-4 py-4 text-center text-muted-foreground">No stickers found</div>
                    )}
                  </div>
                </ScrollArea>
              </TabsContent>
            ))}
          </Tabs>
        </PopoverContent>
      </Popover>
      {value && (
        <div className="relative">
          <img
            src={value.url}
            alt={value.name}
            className="h-16 w-16 object-contain rounded"
          />
          <Button
            variant="ghost"
            size="icon"
            className="h-5 w-5 absolute -top-2 -right-2 p-0"
            onClick={handleClear}
          >
            <X className="h-3 w-3" />
          </Button>
        </div>
      )}
    </div>
  );
}
