import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Form, FormControl, FormField, FormItem } from '@/components/ui/form';
import ImageSelection from './image-selection';
import { useState, useImperativeHandle, forwardRef } from 'react';
import Microphone from '../microphone';
import LanguageSelector from '../language-selector';

interface MessageInputProps {
  onSubmit?: (message: string) => void;
  minLength?: number;
  disabled?: boolean;
}

export interface MessageInputRef {
  setMessage: (message: string) => void;
}

// Define form schema with validation
const formSchema = z.object({
  message: z.string().min(1, 'Message cannot be empty'),
  image: z.any().nullable(),
});

type FormValues = z.infer<typeof formSchema>;

const MessageInput = forwardRef<MessageInputRef, MessageInputProps>(
  ({ onSubmit, minLength = 0, disabled = false }, ref) => {
    const [isMinLengthError, setIsMinLengthError] = useState(false);

    // Initialize form with react-hook-form
    const form = useForm<FormValues>({
      resolver: zodResolver(formSchema),
      defaultValues: {
        message: '',
        image: null,
      },
    });

    // Expose methods to parent component
    useImperativeHandle(ref, () => ({
      setMessage: (message: string) => {
        form.setValue('message', message);
      },
    }));

  const handleSubmit = (data: FormValues) => {
    if (minLength > 0 && data.message.length < minLength) {
      setIsMinLengthError(true);
      return;
    }

    setIsMinLengthError(false);
    console.log(data);

    if (onSubmit) {
      onSubmit(data.message);
    }

    // Clear the form completely after submission
    form.reset({
      message: '',
      image: null,
    });
  };

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(handleSubmit)}
        className="relative w-full"
      >
        <div className="relative flex w-full items-center justify-between px-4 py-4">
          <div className="flex w-full flex-col items-center rounded-lg bg-white dark:bg-[var(--card)] p-2">
            <div className="flex w-full items-center">
              <FormField
                control={form.control}
                name="image"
                disabled
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <ImageSelection
                        value={field.value}
                        onChange={field.onChange}
                        
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <div className="mx-2" />
              <FormField
                control={form.control}
                name="message"
                render={({ field }) => (
                  <FormItem className="flex-1">
                    <FormControl>
                      <Textarea
                        placeholder={disabled ? 'Task expired - cannot send messages' : 'Your message'}
                        className="w-full resize-none shadow-none rounded-lg border-none px-4 focus-visible:ring-0 focus-visible:ring-offset-0 bg-gray-100 dark:bg-gray-800 text-black dark:text-white placeholder:text-gray-500 dark:placeholder:text-gray-400"
                        rows={1}
                        disabled={disabled}
                        {...field}
                        onChange={(e) => {
                          field.onChange(e);
                          if (isMinLengthError && e.target.value.length >= minLength) {
                            setIsMinLengthError(false);
                          }
                        }}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
             <div className="flex items-center bg-gray-100 dark:bg-gray-800 rounded-lg mx-2">
                <LanguageSelector
                  compact={true}
                  className="border-none bg-transparent shadow-none rounded-r-none"
                />
                <Microphone
                  setOutput={(value) => form.setValue('message', value)}
                  setErrorMessage={(value) => console.log(value)}
                />
              </div>

              <Button
                type="submit"
                disabled={disabled}
                className="bg-[#c92cc4] hover:bg-[#9a1c96] dark:bg-[#9a1c96] dark:hover:bg-[#7a1676] text-white disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {disabled ? 'Expired' : 'Send'}
              </Button>
            </div>
            
            {isMinLengthError && (
              <div className="w-full text-red-500 text-sm mt-1 px-2">
                Message must be at least {minLength} characters long
              </div>
            )}
            
            {minLength > 0 && (
              <div className="w-full text-gray-500 text-xs mt-1 px-2 flex justify-end">
                {form.watch('message').length}/{minLength} characters
              </div>
            )}
          </div>
        </div>
      </form>
    </Form>
  );
});

MessageInput.displayName = 'MessageInput';

export default MessageInput;
