interface SystemMessageProps {
  message: string;
  timestamp: string;
}

const SystemMessage = ({ message, timestamp }: SystemMessageProps) => {
  return (

      <div className="bg-yellow-100 border flex justify-between items-center border-yellow-200 rounded-lg px-4 py-2 text-sm text-yellow-800 w-[90%] mx-auto my-4">
        <div>{message}</div>
        <div className="text-right text-xs opacity-70">
          {timestamp}
        </div>
      </div>

  );
};

export default SystemMessage; 