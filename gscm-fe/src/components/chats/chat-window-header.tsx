import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { UserRoundIcon } from 'lucide-react';
import { useEffect, useState } from 'react';
import { CustomerInfo } from '@/mutations/task-mutations';
import { useTaskStore } from '@/stores/task-store';


interface ChatWindowHeaderProps {
  customer: CustomerInfo;
  onTaskExpired?: () => void;
  onExpirationChange?: (isExpired: boolean) => void;
}

export default function ChatWindowHeader({
  customer,
  onTaskExpired,
  onExpirationChange
}: ChatWindowHeaderProps) {
  const [timeLeft, setTimeLeft] = useState(180); // Default 3 minutes in seconds

  // Get task from store
  const { task } = useTaskStore();
  const expiryDate = task?.expiresAt;
  const customerProfilePic = task?.data.siteInfos.metaData.customerProfilePic;

  useEffect(() => {
    // Calculate time left based on expiry date if provided
    if (expiryDate) {
      const calculateTimeLeft = () => {
        const expiry = new Date(expiryDate);
        const now = new Date();
        const diff = Math.max(0, Math.floor((expiry.getTime() - now.getTime()) / 1000));
        return diff;
      };

      // Initial calculation
      setTimeLeft(calculateTimeLeft());

      // Set up interval to update countdown
      const timer = setInterval(() => {
        const newTimeLeft = calculateTimeLeft();
        setTimeLeft(newTimeLeft);
        
        if (newTimeLeft <= 0) {
          clearInterval(timer);
          onExpirationChange?.(true);
          onTaskExpired?.();
        }
      }, 1000);

      return () => clearInterval(timer);
    } else {
      // Fallback to default countdown if no expiry date
      const timer = setInterval(() => {
        setTimeLeft((prev) => {
          if (prev <= 1) {
            clearInterval(timer);
            onExpirationChange?.(true);
            onTaskExpired?.();
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [expiryDate, onTaskExpired, onExpirationChange]);

  const getTimerStyle = () => {
    if (timeLeft > 120) return 'bg-green-500/20 text-green-500 border-green-500/30';
    if (timeLeft > 60) return 'bg-yellow-500/20 text-yellow-500 border-yellow-500/30';
    return 'bg-red-500/20 text-red-500 border-red-500/30';
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className="relative flex h-20 items-center justify-between px-4 border-b border-gray-200 dark:border-gray-700">
      <div className="flex items-center gap-3">
        <div className="relative">
          <Avatar>
            <AvatarImage
              src={customerProfilePic}
              alt={`${customer.username} avatar`}
            />
            <AvatarFallback className="bg-gray-200 dark:bg-gray-700">
              <UserRoundIcon className="h-4 w-4 text-gray-600 dark:text-gray-300" />
            </AvatarFallback>
          </Avatar>
        </div>
        <div>
          <h3 className="font-medium text-gray-900 dark:text-gray-100">
            {customer.username}, {customer.birthDate.age} Years
          </h3>
        </div>
      </div>
      <div className="flex items-center gap-3">
        <Badge className={`text-base font-semibold px-3 py-0.5 rounded-full border ${getTimerStyle()}`}>
          {formatTime(timeLeft)}
        </Badge>
      </div>
    </div>
  );
}
