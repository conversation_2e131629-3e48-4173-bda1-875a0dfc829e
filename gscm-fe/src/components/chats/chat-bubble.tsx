import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { cn } from '@/lib/utils';

interface ChatBubbleProps {
  message: string;
  timestamp: string;
  isOutgoing?: boolean;
  avatarUrl?: string;
  senderInitial?: string;
  imageUrl?: string | null;
}

const ChatBubble = ({
  message,
  timestamp,
  isOutgoing = false,
  avatarUrl,
  senderInitial = 'U',
  imageUrl = null,
}: ChatBubbleProps) => {
  return (
    <div className={cn('mb-2 flex max-w-[80%] items-start gap-2', isOutgoing ? 'ml-auto flex-row-reverse' : 'mr-auto')}>
      {/* Avatar - show for both incoming and outgoing messages */}
      <Avatar className="h-8 w-8">
        <AvatarImage
          src={avatarUrl}
          alt="User avatar"
        />
        <AvatarFallback className={cn('bg-darkbg text-white')}>{senderInitial}</AvatarFallback>
      </Avatar>

      {/* Message Bubble */}
      <div
        className={cn(
          'relative rounded-lg px-3 py-2 text-sm',
          isOutgoing 
            ? 'rounded-tr-none bg-[#eddeed] dark:bg-[#2d1b2d] text-black dark:text-white' 
            : 'rounded-tl-none bg-[#c92cc4] dark:bg-[#9a1c96] text-white'
        )}
      >
        {imageUrl && (
          <img
            src={imageUrl}
            alt="Message image"
            className="mb-2 h-full w-64 rounded-lg object-cover"
          />
        )}
        <div>{message}</div>
        <div className="mt-1 min-w-[58px] text-right text-xs opacity-70">{timestamp}</div>
      </div>
    </div>
  );
};

export default ChatBubble;
