import { ReactNode, useEffect } from "react";
import { Navigate, useLocation } from "react-router-dom";
import { useAuth } from "@/contexts/auth-context";
import { toast } from "sonner";

interface ProtectedRouteProps {
  children: ReactNode;
}

export function ProtectedRoute({ children }: ProtectedRouteProps) {
  const location = useLocation();
  const { isAuthenticated, isLoading } = useAuth();
  
  useEffect(() => {
    if (!isAuthenticated && !isLoading) {
      toast.error("Authentication required", {
        description: "Please log in to access this page",
      });
    }
  }, [isAuthenticated, isLoading]);

  // Show loading state while checking authentication
  if (isLoading) {
    return (
      <div className="flex h-screen w-full items-center justify-center">
        <div className="h-8 w-8 animate-spin rounded-full border-2 border-gray-300 border-t-black" />
      </div>
    );
  }

  if (!isAuthenticated) {
    // Save the current location for redirecting after login
    sessionStorage.setItem("redirectPath", location.pathname + location.search);
    
    // Redirect to login page
    return <Navigate to="/login" replace />;
  }

  return <>{children}</>;
} 