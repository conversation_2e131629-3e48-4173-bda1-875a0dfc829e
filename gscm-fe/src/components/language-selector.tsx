import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import {
  DeeplSupportedLanguage,
  languageMap,
  languageStringForMapItem
} from '@/lib/language-service';
import { useLanguageStore } from '@/stores/language-store';
import { Globe } from 'lucide-react';
import { toast } from 'sonner';

interface LanguageSelectorProps {
  className?: string;
  size?: 'sm' | 'default';
  compact?: boolean; // New prop for compact display (flag + code only)
}

export default function LanguageSelector({
  className = '',
  size = 'default',
  compact = false
}: LanguageSelectorProps) {
  const { language, setLanguage } = useLanguageStore();

  const handleLanguageChange = (value: string) => {
    const selectedLanguage = value as DeeplSupportedLanguage;
    setLanguage(selectedLanguage);

    // Show instructions for Chrome translate since we can't trigger it programmatically
    if (selectedLanguage !== DeeplSupportedLanguage.EN) {
      const languageName = languageMap[selectedLanguage].name;
      const isMac = navigator.userAgent.toUpperCase().indexOf('MAC') >= 0;
      const shortcut = isMac ? 'Cmd+Shift+T' : 'Ctrl+Shift+T';

      toast.info(`Language changed to ${languageName}`, {
        description: `To translate this page: Right-click → "Translate to ${languageName}" or press ${shortcut}`,
        duration: 6000,
        action: {
          label: 'Got it',
          onClick: () => {},
        },
      });
    }
  };

  const getCurrentLanguageDisplay = () => {
    const currentLangData = languageMap[language];
    if (compact) {
      return `${currentLangData.emoji}`;
    }
    return languageStringForMapItem(currentLangData);
  };

  return (
    <Select value={language} onValueChange={handleLanguageChange}>
      <SelectTrigger
        className={`w-auto ${compact ? 'min-w-[60px] h-10 px-2 rounded-none outline-none focus:ring-0 focus:ring-offset-0 focus:outline-none ring-0 focus-visible:ring-0 focus-visible:ring-offset-0 focus-visible:outline-none' : 'min-w-[120px]'} ${className}`}
        size={size}
      >
        <div className="flex items-center gap-2">
          {!compact && <Globe className="h-4 w-4" />}
          <SelectValue placeholder="Select language">
            {getCurrentLanguageDisplay()}
          </SelectValue>
        </div>
      </SelectTrigger>
      <SelectContent>
        {Object.entries(languageMap).map(([langCode, langData]) => (
          <SelectItem
            key={langCode}
            value={langCode}
            className="cursor-pointer"
          >
            {compact ? `${langData.emoji} ${langCode}` : languageStringForMapItem(langData)}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}
