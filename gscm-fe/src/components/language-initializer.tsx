import { useEffect } from 'react';
import { useLanguageStore } from '@/stores/language-store';

interface LanguageInitializerProps {
  children: React.ReactNode;
}

export default function LanguageInitializer({ children }: LanguageInitializerProps) {
  const { language } = useLanguageStore();

  useEffect(() => {
    // Initialize document language on component mount
    if (language) {
      // Set document language attributes
      document.documentElement.lang = getLanguageCode(language);
      document.body.setAttribute('lang', getLanguageCode(language));
      
      // Add meta tag for content language
      let metaTag = document.querySelector('meta[http-equiv="content-language"]') as HTMLMetaElement;
      if (!metaTag) {
        metaTag = document.createElement('meta');
        metaTag.setAttribute('http-equiv', 'content-language');
        document.head.appendChild(metaTag);
      }
      metaTag.setAttribute('content', getLanguageCode(language));
      
      console.log(`Language initialized to: ${getLanguageCode(language)}`);
    }
  }, [language]);

  return <>{children}</>;
}

// Helper function to convert DeepL language codes to standard language codes
function getLanguageCode(language: string): string {
  const languageCodeMap: Record<string, string> = {
    'AR': 'ar',
    'BG': 'bg', 
    'CS': 'cs',
    'DA': 'da',
    'DE': 'de',
    'EL': 'el',
    'EN': 'en',
    'ES': 'es',
    'ET': 'et',
    'FI': 'fi',
    'FR': 'fr',
    'HU': 'hu',
    'ID': 'id',
    'IT': 'it',
    'JA': 'ja',
    'KO': 'ko',
    'LT': 'lt',
    'LV': 'lv',
    'NB': 'no',
    'NL': 'nl',
    'PL': 'pl',
    'PT': 'pt',
    'RO': 'ro',
    'RU': 'ru',
    'SK': 'sk',
    'SL': 'sl',
    'SV': 'sv',
    'TR': 'tr',
    'UK': 'uk',
    'ZH': 'zh',
  };
  
  return languageCodeMap[language] || 'en';
}
