import { But<PERSON> } from "@/components/ui/button";
import { useEffect, useState } from "react";
import { RefreshCwIcon } from "lucide-react";

interface WaiterProps {
  onRefresh: () => void;
  interval: number; // in seconds
}

export default function Waiter({ onRefresh, interval }: WaiterProps) {
  const [timeLeft, setTimeLeft] = useState(interval);

  useEffect(() => {
    // Reset timer whenever interval changes
    setTimeLeft(interval);
    
    const timer = setInterval(() => {
      setTimeLeft((prev) => {
        if (prev <= 1) {
          // When timer reaches 0, trigger the refresh
          onRefresh();
          return interval;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [interval, onRefresh]);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 p-8 rounded-lg shadow-lg max-w-md w-full">
        <div className="flex flex-col items-center gap-6">
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-2">Waiting for next task...</h2>
            <p className="text-gray-500 dark:text-gray-400">
              Next refresh in
            </p>
            <div className="text-4xl font-bold my-4">
              {formatTime(timeLeft)}
            </div>
          </div>
          
          <Button 
            onClick={() => onRefresh()}
            className="flex items-center gap-2"
          >
            <RefreshCwIcon size={16} />
            Refresh Now
          </Button>
        </div>
      </div>
    </div>
  );
} 