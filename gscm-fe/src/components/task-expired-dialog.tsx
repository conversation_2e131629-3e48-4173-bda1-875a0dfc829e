import { Button } from '@/components/ui/button';
import { AlertTriangleIcon, RefreshCwIcon } from 'lucide-react';

interface TaskExpiredDialogProps {
  isOpen: boolean;
  onFetchNewTask: () => void;
}

export default function TaskExpiredDialog({ isOpen, onFetchNewTask }: TaskExpiredDialogProps) {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 p-8 rounded-lg shadow-lg max-w-md w-full mx-4">
        <div className="flex flex-col items-center gap-6">
          <div className="text-center">
            <div className="flex justify-center mb-4">
              <AlertTriangleIcon className="h-16 w-16 text-red-500" />
            </div>
            <h2 className="text-2xl font-bold mb-2 text-gray-900 dark:text-gray-100">
              Task Expired
            </h2>
            <p className="text-gray-500 dark:text-gray-400">
              The task has expired. Do you want to fetch a new task?
            </p>
          </div>
          
          <Button 
            onClick={onFetchNewTask}
            className="flex items-center gap-2 w-full"
          >
            <RefreshCwIcon size={16} />
            Fetch New Task
          </Button>
        </div>
      </div>
    </div>
  );
}
