import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Clock, TrendingUp, TrendingDown, Minus, Trash2 } from 'lucide-react';
import { useTimeTrackingStore, TaskCompletion, formatDuration, calculateEfficiency } from '@/stores/time-tracking-store';
import { useAuth } from '@/contexts/auth-context';
import { motion } from 'motion/react';

interface TimeTrackingPanelProps {
  className?: string;
}

export default function TimeTrackingPanel({ className }: TimeTrackingPanelProps) {
  const { user } = useAuth();
  const { 
    completions, 
    getCurrentTimeProvided, 
    getCurrentTimeTaken, 
    isTimingActive,
    clearCompletions 
  } = useTimeTrackingStore();

  // Filter completions for current user
  const userCompletions = user?.email 
    ? completions.filter(completion => completion.userEmail === user.email)
    : [];

  // Calculate statistics
  const totalTasks = userCompletions.length;
  const averageTimeTaken = totalTasks > 0 
    ? Math.round(userCompletions.reduce((sum, c) => sum + c.timeTaken, 0) / totalTasks)
    : 0;
  const averageEfficiency = totalTasks > 0
    ? Math.round(userCompletions.reduce((sum, c) => sum + calculateEfficiency(c.timeTaken, c.timeProvided), 0) / totalTasks)
    : 0;

  // Current task timing info
  const currentTimeProvided = getCurrentTimeProvided();
  const currentTimeTaken = getCurrentTimeTaken();
  const currentEfficiency = currentTimeProvided > 0 
    ? calculateEfficiency(currentTimeTaken, currentTimeProvided)
    : 0;

  const getEfficiencyIcon = (efficiency: number) => {
    if (efficiency < 70) return <TrendingUp className="w-4 h-4 text-green-500" />;
    if (efficiency > 100) return <TrendingDown className="w-4 h-4 text-red-500" />;
    return <Minus className="w-4 h-4 text-yellow-500" />;
  };

  const getEfficiencyColor = (efficiency: number) => {
    if (efficiency < 70) return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
    if (efficiency > 100) return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
    return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Clock className="w-5 h-5" />
          Time Tracking
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Current Task Timing */}
        {isTimingActive() && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800"
          >
            <h4 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">Current Task</h4>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div>
                <span className="text-gray-600 dark:text-gray-400">Time Taken:</span>
                <p className="font-mono font-semibold">{formatDuration(currentTimeTaken)}</p>
              </div>
              <div>
                <span className="text-gray-600 dark:text-gray-400">Time Provided:</span>
                <p className="font-mono font-semibold">{formatDuration(currentTimeProvided)}</p>
              </div>
            </div>
            <div className="mt-2">
              <div className="flex items-center gap-2">
                {getEfficiencyIcon(currentEfficiency)}
                <Badge className={getEfficiencyColor(currentEfficiency)}>
                  {currentEfficiency}% efficiency
                </Badge>
              </div>
            </div>
          </motion.div>
        )}

        {/* Statistics */}
        <div className="grid grid-cols-3 gap-2 text-center">
          <div className="p-2 bg-gray-50 dark:bg-gray-800 rounded">
            <p className="text-2xl font-bold text-blue-600">{totalTasks}</p>
            <p className="text-xs text-gray-600 dark:text-gray-400">Tasks</p>
          </div>
          <div className="p-2 bg-gray-50 dark:bg-gray-800 rounded">
            <p className="text-2xl font-bold text-green-600">{formatDuration(averageTimeTaken)}</p>
            <p className="text-xs text-gray-600 dark:text-gray-400">Avg Time</p>
          </div>
          <div className="p-2 bg-gray-50 dark:bg-gray-800 rounded">
            <p className="text-2xl font-bold text-purple-600">{averageEfficiency}%</p>
            <p className="text-xs text-gray-600 dark:text-gray-400">Avg Efficiency</p>
          </div>
        </div>

        {/* Recent Completions */}
        {userCompletions.length > 0 && (
          <div>
            <div className="flex items-center justify-between mb-2">
              <h4 className="font-semibold">Recent Tasks</h4>
              <Button
                variant="ghost"
                size="sm"
                onClick={clearCompletions}
                className="text-red-600 hover:text-red-700 hover:bg-red-50"
              >
                <Trash2 className="w-4 h-4" />
              </Button>
            </div>
            <div className="space-y-2 max-h-40 overflow-y-auto">
              {userCompletions.slice(-5).reverse().map((completion, index) => (
                <motion.div
                  key={completion.taskId}
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="p-2 bg-gray-50 dark:bg-gray-800 rounded text-sm"
                >
                  <div className="flex items-center justify-between mb-1">
                    <span className="font-mono text-xs text-gray-500">
                      {completion.taskId.slice(-8)}
                    </span>
                    <Badge className={getEfficiencyColor(calculateEfficiency(completion.timeTaken, completion.timeProvided))}>
                      {calculateEfficiency(completion.timeTaken, completion.timeProvided)}%
                    </Badge>
                  </div>
                  <div className="flex justify-between text-xs text-gray-600 dark:text-gray-400">
                    <span>{formatDuration(completion.timeTaken)} taken</span>
                    <span>{formatDuration(completion.timeProvided)} provided</span>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        )}

        {userCompletions.length === 0 && !isTimingActive() && (
          <div className="text-center py-4 text-gray-500 dark:text-gray-400">
            <Clock className="w-8 h-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">No task completions yet</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
