import { Card } from '@/components/ui/card';
import ChatWindow from '@/components/chats/chat-window';
import { useTaskStore } from '@/stores/task-store';

interface ChatPanelProps {
  onSubmit: (response: string) => void;
  onTaskExpired?: () => void;
}

export default function ChatPanel({ onSubmit, onTaskExpired }: ChatPanelProps) {
  const { task } = useTaskStore();
  const customerInfo = task?.data.siteInfos.metaData.customerInfo;
  return (
    <Card className="p-0 h-full transition-all duration-300 hover:shadow-lg">
      {task && customerInfo ? (
        <ChatWindow
          customer={customerInfo}
          messages={[]}
          onSubmit={onSubmit}
          onTaskExpired={onTaskExpired}
        />
      ) : (
        <div className="flex items-center justify-center h-full">
          <p className="text-gray-500">No task available</p>
        </div>
      )}
    </Card>
  );
}
