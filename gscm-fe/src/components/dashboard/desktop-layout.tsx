import { motion } from 'motion/react';
import UserInfoPanel from './user-info-panel';
import NotesPanel from './notes-panel';
import UpdatesPanel from './updates-panel';
import ChatPanel from './chat-panel';
import { useTaskStore } from '@/stores/task-store';

interface DesktopLayoutProps {
  onSubmit: (response: string) => void;
  onTaskExpired?: () => void;
}

export default function DesktopLayout({
  onSubmit,
  onTaskExpired
}: DesktopLayoutProps) {
  // Get task from store
  const { task } = useTaskStore();

  // Extract data from task
  const customerInfo = task?.data.siteInfos.metaData.customerInfo;
  const moderatorInfo = task?.data.siteInfos.metaData.moderatorInfo;
  const customerNotes = task?.data.siteInfos.metaData.customerNotes;
  const moderatorNotes = task?.data.siteInfos.metaData.moderatorNotes;
  const customerProfilePic = task?.data.siteInfos.metaData.customerProfilePic;
  const moderatorProfilePic = task?.data.siteInfos.metaData.moderatorProfilePic;
  return (
    <div className="hidden md:grid grid-cols-4 gap-4 p-4 pt-16 min-h-screen overflow-hidden">
      {/* Moderator Column */}
      <motion.div
        className="col-span-1 flex flex-col gap-4 h-screen overflow-y-auto"
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.5 }}
      >
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          <UserInfoPanel
            userInfo={moderatorInfo}
            title="Moderator Info"
            avatarUrl={moderatorProfilePic || "https://mighty.tools/mockmind-api/content/human/125.jpg"}
            isMobile={false}
          />
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
        >
          <NotesPanel notes={moderatorNotes} title="Moderator Notes" />
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.3 }}
        >
          <UpdatesPanel
            updates={task?.data.siteInfos.metaData.moderatorUpdates}
            title="Moderator Updates"
          />
        </motion.div>
      </motion.div>

      {/* Chat Window */}
      <motion.div
        className="col-span-2 max-h-[87vh] transform translate-y-16"
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <ChatPanel
          onSubmit={onSubmit}
          onTaskExpired={onTaskExpired}
        />
      </motion.div>

      {/* User Column */}
      <motion.div
        className="col-span-1 flex flex-col gap-4"
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.5 }}
      >
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          <UserInfoPanel
            userInfo={customerInfo}
            title="User Info"
            avatarUrl={customerProfilePic || "https://mighty.tools/mockmind-api/content/human/45.jpg"}
            isMobile={false}
          />
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
        >
          <NotesPanel notes={customerNotes} title="User Notes" />
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.3 }}
        >
          <UpdatesPanel
            updates={task?.data.siteInfos.metaData.customerUpdates}
            title="User Updates"
          />
        </motion.div>
      </motion.div>
    </div>
  );
}
