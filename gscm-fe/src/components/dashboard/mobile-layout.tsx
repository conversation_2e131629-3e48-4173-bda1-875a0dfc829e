import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { MessageSquare, Shield, Users } from 'lucide-react';
import { motion } from 'motion/react';
import UserInfoPanel from './user-info-panel';
import NotesPanel from './notes-panel';
import UpdatesPanel from './updates-panel';
import ChatPanel from './chat-panel';
import { useTaskStore } from '@/stores/task-store';

interface MobileLayoutProps {
  onSubmit: (response: string) => void;
  onTaskExpired?: () => void;
}

export default function MobileLayout({
  onSubmit,
  onTaskExpired
}: MobileLayoutProps) {
  // Get task from store
  const { task } = useTaskStore();

  // Extract data from task
  const customerInfo = task?.data.siteInfos.metaData.customerInfo;
  const moderatorInfo = task?.data.siteInfos.metaData.moderatorInfo;
  const customerNotes = task?.data.siteInfos.metaData.customerNotes;
  const moderatorNotes = task?.data.siteInfos.metaData.moderatorNotes;
  const customerProfilePic = task?.data.siteInfos.metaData.customerProfilePic;
  const moderatorProfilePic = task?.data.siteInfos.metaData.moderatorProfilePic;
  return (
    <div className="md:hidden p-2 pt-12">
      <Tabs defaultValue="chat" className="h-[calc(100vh-3rem)]">
        <TabsList className="grid w-full grid-cols-3 mb-4">
          <TabsTrigger value="moderator" className="flex items-center gap-2">
            <Shield className="w-4 h-4" />
            <span className="hidden sm:inline">Moderator</span>
          </TabsTrigger>
          <TabsTrigger value="chat" className="flex items-center gap-2">
            <MessageSquare className="w-4 h-4" />
            <span className="hidden sm:inline">Chat</span>
          </TabsTrigger>
          <TabsTrigger value="user" className="flex items-center gap-2">
            <Users className="w-4 h-4" />
            <span className="hidden sm:inline">User</span>
          </TabsTrigger>
        </TabsList>

        {/* Moderator Tab Content */}
        <TabsContent value="moderator" className="h-full overflow-y-auto">
          <div className="flex flex-col gap-4">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.1 }}
            >
              <UserInfoPanel
                userInfo={moderatorInfo}
                title="Moderator Info"
                avatarUrl={moderatorProfilePic || "https://mighty.tools/mockmind-api/content/human/125.jpg"}
                isMobile={true}
              />
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.2 }}
            >
              <NotesPanel notes={moderatorNotes} title="Moderator Notes" />
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.3 }}
            >
              <UpdatesPanel
                updates={task?.data.siteInfos.metaData.moderatorUpdates}
                title="Moderator Updates"
              />
            </motion.div>
          </div>
        </TabsContent>

        {/* Chat Tab Content */}
        <TabsContent value="chat" className="h-full">
          <motion.div
            className="h-full"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <ChatPanel
              onSubmit={onSubmit}
              onTaskExpired={onTaskExpired}
            />
          </motion.div>
        </TabsContent>

        {/* User Tab Content */}
        <TabsContent value="user" className="h-full overflow-y-auto">
          <div className="flex flex-col gap-4">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.1 }}
            >
              <UserInfoPanel
                userInfo={customerInfo}
                title="User Info"
                avatarUrl={customerProfilePic || "https://mighty.tools/mockmind-api/content/human/45.jpg"}
                isMobile={true}
              />
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.2 }}
            >
              <NotesPanel notes={customerNotes} title="User Notes" />
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.3 }}
            >
              <UpdatesPanel
                updates={task?.data.siteInfos.metaData.customerUpdates}
                title="User Updates"
              />
            </motion.div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
