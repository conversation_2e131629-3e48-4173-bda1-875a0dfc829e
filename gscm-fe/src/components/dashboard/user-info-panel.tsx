import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { motion } from 'motion/react';
import { CustomerInfo, ModeratorInfo } from '@/mutations/task-mutations';

interface UserInfoPanelProps {
  userInfo: CustomerInfo | ModeratorInfo | null | undefined;
  title: string;
  avatarUrl: string;
  isMobile?: boolean;
}

export default function UserInfoPanel({ 
  userInfo, 
  title, 
  avatarUrl, 
  isMobile = false 
}: UserInfoPanelProps) {
  return (
    <Card className="transition-all duration-300 hover:shadow-lg">
      <CardHeader>
        <p className="text-lg font-bold">{title}</p>
      </CardHeader>
      <CardContent>
        <div className={`flex ${isMobile ? 'flex-col' : 'flex-row'} gap-${isMobile ? '3' : '6'}`}>
          {userInfo ? (
            <motion.img
              whileHover={{ scale: 1.05 }}
              transition={{ type: 'spring', stiffness: 300 }}
              src={avatarUrl}
              alt={`${title} Avatar`}
              className={`${isMobile ? 'w-20 h-20 mx-auto' : 'w-32 h-32'} rounded-2xl`}
            />
          ) : (
            <p className="text-gray-500">No {title.toLowerCase()} info</p>
          )}
          {userInfo && (
            <div className={`flex flex-col gap-2 ${isMobile ? 'text-center' : ''} w-full`}>
              <p className={`${isMobile ? 'text-lg' : 'text-xl'} font-semibold`}>
                {userInfo.username}
              </p>
              <p className="text-sm text-gray-500">Age: {userInfo.birthDate.age}</p>
              <p className="text-sm text-gray-500">Username: {userInfo.username}</p>
            </div>
          )}
        </div>
        
        {/* Additional profile information for desktop moderator view */}
        {userInfo && !isMobile && (
          <div className="flex flex-col gap-2 w-full py-4">
            <p className="text-sm">{userInfo.profileText || 'No profile text available'}</p>
            {Object.keys(userInfo).map((key) =>
              key !== 'name' && 
              key !== 'username' && 
              key !== 'birthDate' && 
              key !== 'profileText' && 
              key !== 'hasPictures' && 
              key !== 'hasProfilePic' ? (
                <p key={key} className="text-sm text-gray-500">
                  <span className="font-semibold capitalize">
                    {key.replace(/([A-Z])/g, ' $1').trim()}:
                  </span>{' '}
                  {typeof userInfo[key as keyof typeof userInfo] === 'object'
                    ? JSON.stringify(userInfo[key as keyof typeof userInfo])
                    : String(userInfo[key as keyof typeof userInfo])
                  }
                </p>
              ) : null
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
