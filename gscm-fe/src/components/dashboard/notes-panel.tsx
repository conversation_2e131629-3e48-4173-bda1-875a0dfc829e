import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Notes } from '@/mutations/task-mutations';

interface NotesPanelProps {
  notes: Notes | null | undefined;
  title: string;
}

export default function NotesPanel({ notes, title }: NotesPanelProps) {
  return (
    <Card className="transition-all duration-300 hover:shadow-lg">
      <CardHeader>
        <p className="text-lg font-bold">{title}</p>
      </CardHeader>
      <CardContent>
        {notes?.rawText ? (
          <p className="text-sm text-gray-500">{notes.rawText}</p>
        ) : (
          <p className="text-sm text-gray-500">No notes available.</p>
        )}
      </CardContent>
    </Card>
  );
}
