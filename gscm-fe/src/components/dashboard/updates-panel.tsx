import { Card, CardContent, CardHeader } from '@/components/ui/card';

interface UpdatesPanelProps {
  updates: string[] | null | undefined;
  title: string;
}

export default function UpdatesPanel({ updates, title }: UpdatesPanelProps) {
  return (
    <Card className="transition-all duration-300 hover:shadow-lg">
      <CardHeader>
        <p className="text-lg font-bold">{title}</p>
      </CardHeader>
      <CardContent>
        {updates && updates.length > 0 ? (
          updates.map((update: string, index: number) => (
            <p key={index} className="text-sm text-gray-500">
              {update}
            </p>
          ))
        ) : (
          <p className="text-sm text-gray-500">No updates available.</p>
        )}
      </CardContent>
    </Card>
  );
}
