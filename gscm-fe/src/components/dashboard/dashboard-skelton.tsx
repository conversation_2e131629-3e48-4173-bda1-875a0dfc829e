import { Dock, DockIcon } from '@/components/magicui/dock';
import { HomeIcon, LogOutIcon, UserIcon, MessageSquare, Shield, Users } from 'lucide-react';
import { ThemeSwitcher } from '@/components/theme-switcher';
import { Skeleton } from '@/components/ui/skeleton';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useAuth } from '@/contexts/auth-context';

export default function DashboardSkeleton() {
  const { logout } = useAuth();
  return (
    <div className="bg-[#330022] min-h-screen dark:bg-[#17000f]">
      <Dock
        direction="middle"
        className="absolute -top-4 left-1/2 -translate-x-1/2 z-10 h-12 bg-white md:h-12"
        iconMagnification={60}
        iconDistance={100}
      >
        <DockIcon>
          <HomeIcon className="w-4 h-4 md:w-5 md:h-5" />
        </DockIcon>
        <DockIcon>
          <UserIcon className="w-4 h-4 md:w-5 md:h-5" />
        </DockIcon>
        <DockIcon>
          <ThemeSwitcher />
        </DockIcon>
        <DockIcon onClick={logout}>
          <LogOutIcon className="w-4 h-4 md:w-5 md:h-5" />
        </DockIcon>
      </Dock>

      {/* Mobile Tabs Layout */}
      <div className="md:hidden p-2 pt-12">
        <Tabs defaultValue="chat" className="h-[calc(100vh-3rem)]">
          <TabsList className="grid w-full grid-cols-3 mb-4">
            <TabsTrigger value="moderator" className="flex items-center gap-2">
              <Shield className="w-4 h-4" />
              <span className="hidden sm:inline">Moderator</span>
            </TabsTrigger>
            <TabsTrigger value="chat" className="flex items-center gap-2">
              <MessageSquare className="w-4 h-4" />
              <span className="hidden sm:inline">Chat</span>
            </TabsTrigger>
            <TabsTrigger value="user" className="flex items-center gap-2">
              <Users className="w-4 h-4" />
              <span className="hidden sm:inline">User</span>
            </TabsTrigger>
          </TabsList>

          {/* Moderator Tab Content */}
          <TabsContent value="moderator" className="h-full overflow-y-auto">
            <div className="flex flex-col gap-4">
              <Card className="transition-all duration-300">
                <CardHeader>
                  <Skeleton className="h-6 w-2/3" />
                </CardHeader>
                <CardContent>
                  <div className="flex flex-col gap-3">
                    <Skeleton className="w-20 h-20 rounded-2xl mx-auto" />
                    <div className="flex flex-col gap-2 text-center">
                      <Skeleton className="h-5 w-24 mx-auto" />
                      <Skeleton className="h-3 w-32 mx-auto" />
                      <Skeleton className="h-3 w-40 mx-auto" />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="transition-all duration-300">
                <CardHeader>
                  <Skeleton className="h-6 w-2/3" />
                </CardHeader>
                <CardContent>
                  <div className="flex flex-col gap-2">
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-3/4" />
                  </div>
                </CardContent>
              </Card>

              <Card className="transition-all duration-300">
                <CardHeader>
                  <Skeleton className="h-6 w-2/3" />
                </CardHeader>
                <CardContent>
                  <div className="flex flex-col gap-2">
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-3/4" />
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Chat Tab Content */}
          <TabsContent value="chat" className="h-full">
            <Card className="p-3 h-full">
              <div className="flex flex-col h-full">
                <div className="flex-1 mb-4">
                  <div className="space-y-4">
                    <Skeleton className="h-16 w-3/4" />
                    <Skeleton className="h-16 w-2/3 ml-auto" />
                    <Skeleton className="h-16 w-3/4" />
                  </div>
                </div>
                <Skeleton className="h-12 w-full rounded-lg" />
              </div>
            </Card>
          </TabsContent>

          {/* User Tab Content */}
          <TabsContent value="user" className="h-full overflow-y-auto">
            <div className="flex flex-col gap-4">
              <Card className="transition-all duration-300">
                <CardHeader>
                  <Skeleton className="h-6 w-2/3" />
                </CardHeader>
                <CardContent>
                  <div className="flex flex-col gap-3">
                    <Skeleton className="w-20 h-20 rounded-2xl mx-auto" />
                    <div className="flex flex-col gap-2 text-center">
                      <Skeleton className="h-5 w-24 mx-auto" />
                      <Skeleton className="h-3 w-32 mx-auto" />
                      <Skeleton className="h-3 w-40 mx-auto" />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="transition-all duration-300">
                <CardHeader>
                  <Skeleton className="h-6 w-2/3" />
                </CardHeader>
                <CardContent>
                  <div className="flex flex-col gap-2">
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-3/4" />
                  </div>
                </CardContent>
              </Card>

              <Card className="transition-all duration-300">
                <CardHeader>
                  <Skeleton className="h-6 w-2/3" />
                </CardHeader>
                <CardContent>
                  <div className="flex flex-col gap-2">
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-3/4" />
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>

      {/* Desktop Grid Layout */}
      <div className="hidden md:grid grid-cols-4 gap-4 p-4 pt-16 min-h-screen overflow-hidden">
        {/* Moderator Column */}
        <div className="col-span-1 flex flex-col gap-4 h-screen overflow-y-auto">
          <Card className="transition-all duration-300">
            <CardHeader>
              <Skeleton className="h-6 w-2/3" />
            </CardHeader>
            <CardContent>
              <div className="flex flex-row gap-6">
                <Skeleton className="w-32 h-32 rounded-2xl" />
                <div className="flex flex-col gap-2 w-full">
                  <Skeleton className="h-6 w-24" />
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-4 w-40" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="transition-all duration-300">
            <CardHeader>
              <Skeleton className="h-6 w-2/3" />
            </CardHeader>
            <CardContent>
              <div className="flex flex-col gap-2">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
              </div>
            </CardContent>
          </Card>

          <Card className="transition-all duration-300">
            <CardHeader>
              <Skeleton className="h-6 w-2/3" />
            </CardHeader>
            <CardContent>
              <div className="flex flex-col gap-2">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Chat Column */}
        <div className="col-span-2 max-h-[87vh] transform translate-y-16">
          <Card className="p-6 h-full">
            <div className="flex flex-col h-full">
              <div className="flex-1 mb-4">
                <div className="space-y-4">
                  <Skeleton className="h-16 w-3/4" />
                  <Skeleton className="h-16 w-2/3 ml-auto" />
                  <Skeleton className="h-16 w-3/4" />
                </div>
              </div>
              <Skeleton className="h-12 w-full rounded-lg" />
            </div>
          </Card>
        </div>

        {/* User Column */}
        <div className="col-span-1 flex flex-col gap-4">
          <Card className="transition-all duration-300">
            <CardHeader>
              <Skeleton className="h-6 w-2/3" />
            </CardHeader>
            <CardContent>
              <div className="flex flex-row gap-6">
                <Skeleton className="w-32 h-32 rounded-2xl" />
                <div className="flex flex-col gap-2 w-full">
                  <Skeleton className="h-6 w-24" />
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-4 w-40" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="transition-all duration-300">
            <CardHeader>
              <Skeleton className="h-6 w-2/3" />
            </CardHeader>
            <CardContent>
              <div className="flex flex-col gap-2">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
              </div>
            </CardContent>
          </Card>

          <Card className="transition-all duration-300">
            <CardHeader>
              <Skeleton className="h-6 w-2/3" />
            </CardHeader>
            <CardContent>
              <div className="flex flex-col gap-2">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
