import tokenManager from './token-manager';
import { toast } from 'sonner';

// Types for request options
type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';

interface RequestOptions {
  method?: HttpMethod;
  headers?: Record<string, string>;
  body?: unknown;
  requiresAuth?: boolean;
  skipErrorToast?: boolean;
}

// Error response type
interface ApiErrorResponse {
  message?: string;
  error?: string;
  status?: number;
  errors?: Record<string, string[]>;
}

// Base API URLs
const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';
const VITE_TASK_API_URL = import.meta.env.VITE_TASK_API_URL || 'http://localhost:3000';

/**
 * Handles API requests with authentication and error handling
 */
const apiClient = {
  /**
   * Make an authenticated API request
   */
  async request<T>(
    endpoint: string,
    options: RequestOptions = {},
    baseUrl: string = API_URL
  ): Promise<T> {
    const {
      method = 'GET',
      headers = {},
      body,
      requiresAuth = true,
      skipErrorToast = false
    } = options;

    // Build request URL
    const url = endpoint.startsWith('http') ? endpoint : `${baseUrl}${endpoint}`;

    // Prepare headers
    const requestHeaders: Record<string, string> = {
      'Content-Type': 'application/json',
      ...headers
    };

    // Add auth token if required
    if (requiresAuth) {
      try {
        const token = await tokenManager.getValidAccessToken();
        if (token) {
          requestHeaders['Authorization'] = `Bearer ${token}`;
        }
      } catch (error) {
        console.error('Auth token error:', error);
        // Handle auth errors (e.g., redirect to login)
        if (!skipErrorToast) {
          toast.error('Authentication error', {
            description: 'Please log in again'
          });
        }
        throw new Error('Authentication failed');
      }
    }

    // Prepare request config
    const config: RequestInit = {
      method,
      headers: requestHeaders
    };

    // Add request body for non-GET requests
    if (body && method !== 'GET') {
      config.body = JSON.stringify(body);
    }

    try {
      const response = await fetch(url, config);

      // Handle unsuccessful responses
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({
          error: 'An unknown error occurred'
        })) as ApiErrorResponse;

        // Handle specific status codes
        if (response.status === 401) {
          // Unauthorized - clear tokens and show toast
          tokenManager.clearTokens();
          if (!skipErrorToast) {
            toast.error('Session expired', {
              description: 'Please log in again'
            });
          }
        } else if (!skipErrorToast) {
          // Show toast for other errors
          toast.error('Request failed', {
            description: errorData.error || errorData.message || `Error ${response.status}`
          });
        }

        // Create an error object with response details that can be accessed in onError
        const error = new Error(errorData.error || errorData.message || `HTTP error ${response.status}`);
        // Add response property to the error object
        (error as Error & { response: { status: number; data: ApiErrorResponse } }).response = {
          status: response.status,
          data: errorData
        };
        throw error;
      }

      // Parse JSON response or return empty object if no content
      const data = response.status !== 204 ? await response.json() : {};
      return data as T;
    } catch (error) {
      // Log error and rethrow
      console.error('API request error:', error);
      throw error;
    }
  },

  /**
   * Convenience methods for different HTTP methods
   */
  async get<T>(endpoint: string, options: Omit<RequestOptions, 'method' | 'body'> = {}): Promise<T> {
    return this.request<T>(endpoint, { ...options, method: 'GET' }, API_URL);
  },

  async post<T>(
    endpoint: string, 
    data?: unknown, 
    options: Omit<RequestOptions, 'method'> = {}
  ): Promise<T> {
    return this.request<T>(endpoint, {
      ...options,
      method: 'POST',
      body: data
    }, API_URL);
  },

  async put<T>(
    endpoint: string, 
    data?: unknown, 
    options: Omit<RequestOptions, 'method'> = {}
  ): Promise<T> {
    return this.request<T>(endpoint, {
      ...options,
      method: 'PUT',
      body: data
    }, API_URL);
  },

  async delete<T>(endpoint: string, options: Omit<RequestOptions, 'method'> = {}): Promise<T> {
    return this.request<T>(endpoint, {
      ...options,
      method: 'DELETE'
    }, API_URL);
  },

  async patch<T>(
    endpoint: string, 
    data?: unknown, 
    options: Omit<RequestOptions, 'method'> = {}
  ): Promise<T> {
    return this.request<T>(endpoint, {
      ...options,
      method: 'PATCH',
      body: data
    }, API_URL);
  },

  // FormData request method for file uploads with authentication
  async postFormData<T>(
    endpoint: string,
    formData: FormData,
    options: Omit<RequestOptions, 'method' | 'body'> = {},
    baseUrl: string = API_URL
  ): Promise<T> {
    const {
      headers = {},
      requiresAuth = true,
      skipErrorToast = false
    } = options;

    // Build request URL
    const url = endpoint.startsWith('http') ? endpoint : `${baseUrl}${endpoint}`;

    // Prepare headers (don't set Content-Type for FormData - browser will set it with boundary)
    const requestHeaders: Record<string, string> = {
      ...headers
    };

    // Add auth token if required
    if (requiresAuth) {
      try {
        const token = await tokenManager.getValidAccessToken();
        if (token) {
          requestHeaders['Authorization'] = `Bearer ${token}`;
        }
      } catch (error) {
        console.error('Auth token error:', error);
        if (!skipErrorToast) {
          toast.error('Authentication error', {
            description: 'Please log in again'
          });
        }
        throw new Error('Authentication failed');
      }
    }

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: requestHeaders,
        body: formData
      });

      if (!response.ok) {
        const errorData: ApiErrorResponse = await response.json().catch(() => ({}));

        if (!skipErrorToast) {
          toast.error('Request failed', {
            description: errorData.message || errorData.error || `HTTP error ${response.status}`
          });
        }

        const error = new Error(errorData.error || errorData.message || `HTTP error ${response.status}`);
        (error as Error & { response: { status: number; data: ApiErrorResponse } }).response = {
          status: response.status,
          data: errorData
        };
        throw error;
      }

      const data = response.status !== 204 ? await response.json() : {};
      return data as T;
    } catch (error) {
      console.error('FormData API request error:', error);
      throw error;
    }
  },

  // Task API specific methods
  task: {
    async get<T>(endpoint: string, options: Omit<RequestOptions, 'method' | 'body'> = {}): Promise<T> {
      return apiClient.request<T>(endpoint, { ...options, method: 'GET' }, VITE_TASK_API_URL);
    },

    async post<T>(
      endpoint: string,
      data?: unknown,
      options: Omit<RequestOptions, 'method'> = {}
    ): Promise<T> {
      return apiClient.request<T>(endpoint, {
        ...options,
        method: 'POST',
        body: data
      }, VITE_TASK_API_URL);
    }
  }
};

export default apiClient; 