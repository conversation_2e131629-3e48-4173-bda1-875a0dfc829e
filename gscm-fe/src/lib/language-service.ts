enum DeeplSupportedLanguage {
    AR = "AR",
    BG = "BG",
    CS = "CS",
    DA = "DA",
    DE = "DE",
    EL = "EL",
    EN = "EN",
    ES = "ES",
    ET = "ET",
    FI = "FI",
    FR = "FR",
    HU = "HU",
    ID = "ID",
    IT = "IT",
    JA = "JA",
    KO = "KO",
    LT = "LT",
    LV = "LV",
    NB = "NB",
    NL = "NL",
    PL = "PL",
    PT = "PT",
    RO = "RO",
    RU = "RU",
    SK = "SK",
    SL = "SL",
    SV = "SV",
    TR = "TR",
    UK = "UK",
    ZH = "ZH",
}

const languageMap: {
    [key in DeeplSupportedLanguage]: { emoji: string; name: string };
} = {
    AR: { emoji: "🇸🇦", name: "Arabic" },
    BG: { emoji: "🇧🇬", name: "Bulgarian" },
    CS: { emoji: "🇨🇿", name: "Czech" },
    DA: { emoji: "🇩🇰", name: "Danish" },
    DE: { emoji: "🇩🇪", name: "German" },
    EL: { emoji: "🇬🇷", name: "Greek" },
    EN: { emoji: "🇬🇧", name: "English" },
    ES: { emoji: "🇪🇸", name: "Spanish" },
    ET: { emoji: "🇪🇪", name: "Estonian" },
    FI: { emoji: "🇫🇮", name: "Finnish" },
    FR: { emoji: "🇫🇷", name: "French" },
    HU: { emoji: "🇭🇺", name: "Hungarian" },
    ID: { emoji: "🇮🇩", name: "Indonesian" },
    IT: { emoji: "🇮🇹", name: "Italian" },
    JA: { emoji: "🇯🇵", name: "Japanese" },
    KO: { emoji: "🇰🇷", name: "Korean" },
    LT: { emoji: "🇱🇹", name: "Lithuanian" },
    LV: { emoji: "🇱🇻", name: "Latvian" },
    NB: { emoji: "🇳🇴", name: "Norwegian (Bokmål)" },
    NL: { emoji: "🇳🇱", name: "Dutch" },
    PL: { emoji: "🇵🇱", name: "Polish" },
    PT: { emoji: "🇵🇹", name: "Portuguese" },
    RO: { emoji: "🇷🇴", name: "Romanian" },
    RU: { emoji: "🇷🇺", name: "Russian" },
    SK: { emoji: "🇸🇰", name: "Slovak" },
    SL: { emoji: "🇸🇮", name: "Slovenian" },
    SV: { emoji: "🇸🇪", name: "Swedish" },
    TR: { emoji: "🇹🇷", name: "Turkish" },
    UK: { emoji: "🇺🇦", name: "Ukrainian" },
    ZH: { emoji: "🇨🇳", name: "Chinese" },
};

function languageStringForMapItem({
    emoji,
    name,
}: {
    emoji: string;
    name: string;
}): string {
    return `${emoji} • ${name}`;
}

export { DeeplSupportedLanguage, languageStringForMapItem, languageMap };
