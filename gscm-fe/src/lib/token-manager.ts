// Types
interface TokenData {
  access_token: string;
  refresh_token: string;
  expires_at: number;
}

const createTokenManager = () => {
  
  let refreshPromise: Promise<TokenData> | null = null;
  let refreshing = false;

  // Get the auth tokens from localStorage
  const getTokens = (): TokenData | null => {
    if (typeof window === 'undefined') return null;

    const access_token = localStorage.getItem('access_token');
    const refresh_token = localStorage.getItem('refresh_token');
    const expires_at = localStorage.getItem('expires_at');

    if (!access_token || !refresh_token || !expires_at) {
      return null;
    }

    return {
      access_token,
      refresh_token,
      expires_at: parseInt(expires_at, 10),
    };
  };

  // Save tokens to localStorage
  const saveTokens = (tokens: TokenData): void => {
    localStorage.setItem('access_token', tokens.access_token);
    localStorage.setItem('refresh_token', tokens.refresh_token);
    localStorage.setItem('expires_at', tokens.expires_at.toString());
  };

  // Clear all tokens
  const clearTokens = (): void => {
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
    localStorage.removeItem('expires_at');
    localStorage.removeItem('user');
  };

  // Check if access token is expired
  const isTokenExpired = (): boolean => {
    const tokens = getTokens();
    if (!tokens) return true;

    // Add a buffer of 60 seconds to account for network delay
    const buffer = 60;
    const now = Math.floor(Date.now() / 1000);
    
    return tokens.expires_at <= (now + buffer);
  };

  // Check if the user is authenticated
  const isAuthenticated = (): boolean => {
    return !isTokenExpired();
  };

  // Refresh the access token
  const refreshAccessToken = async (): Promise<TokenData> => {
    // If already refreshing, return the existing promise
    if (refreshing && refreshPromise) {
      return refreshPromise;
    }

    refreshing = true;

    const tokens = getTokens();
    if (!tokens || !tokens.refresh_token) {
      refreshing = false;
      return Promise.reject(new Error('No refresh token available'));
    }

    // Create a new refresh promise
    refreshPromise = new Promise<TokenData>((resolve, reject) => {
      const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';
      
      fetch(`${API_URL}/auth/refresh`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ refresh_token: tokens.refresh_token }),
      })
        .then(response => {
          if (!response.ok) {
            return response.json().then(error => {
              throw new Error(error.message || 'Failed to refresh token');
            });
          }
          return response.json();
        })
        .then(data => {
          // Save the new tokens
          saveTokens({
            access_token: data.access_token,
            refresh_token: data.refresh_token,
            expires_at: data.expires_at,
          });
          resolve(data);
        })
        .catch(error => {
          // Clear tokens if refresh fails
          clearTokens();
          reject(error);
        })
        .finally(() => {
          refreshing = false;
          refreshPromise = null;
        });
    });

    return refreshPromise;
  };

  // Get a valid access token, refreshing if needed
  const getValidAccessToken = async (): Promise<string> => {
    if (isTokenExpired()) {
      try {
        const refreshedTokens = await refreshAccessToken();
        return refreshedTokens.access_token;
      } catch (error) {
        clearTokens();
        throw error;
      }
    }

    const tokens = getTokens();
    return tokens?.access_token ?? '';
  };

  // Return the public API
  return {
    getTokens,
    saveTokens,
    clearTokens,
    isTokenExpired,
    isAuthenticated,
    refreshAccessToken,
    getValidAccessToken
  };
};

const tokenManager = createTokenManager();

export default tokenManager;
export type { TokenData }; 