import { useMutation, useQueryClient } from "@tanstack/react-query";
import apiClient from "@/lib/api-client";
import tokenManager from "@/lib/token-manager";

// Types
interface LoginCredentials {
  email: string;
  password: string;
}

interface RegisterCredentials {
  username: string;
  password: string;
}

interface AuthResponse {
  access_token: string;
  refresh_token: string;
  expires_at: number;
  user: {
    id: string;
    email: string;
  };
}

interface TokenRefreshRequest {
  refresh_token: string;
}

// Login mutation
export function useLoginMutation() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (credentials: LoginCredentials): Promise<AuthResponse> => {
      const data = await apiClient.post<AuthResponse>(
        '/auth/login', 
        credentials, 
        { requiresAuth: false, skipErrorToast: true }
      );
      
      // Store tokens in localStorage
      tokenManager.saveTokens({
        access_token: data.access_token,
        refresh_token: data.refresh_token,
        expires_at: data.expires_at,
      });
      
      return data;
    },
    onSuccess: () => {
      // Invalidate relevant queries when login is successful
      queryClient.invalidateQueries({ queryKey: ["user"] });
    },
  });
}

// Register mutation
export function useRegisterMutation() {
  return useMutation({
    mutationFn: async (credentials: RegisterCredentials) => {
      return apiClient.post('/auth/register', {
        username: credentials.username,
        password: credentials.password
      }, { requiresAuth: false, skipErrorToast: true });
    },
  });
}

// Token refresh mutation
export function useRefreshTokenMutation() {
  return useMutation({
    mutationFn: async ({ refresh_token }: TokenRefreshRequest): Promise<AuthResponse> => {
      const data = await apiClient.post<AuthResponse>(
        '/auth/refresh',
        { refresh_token },
        { requiresAuth: false, skipErrorToast: true }
      );
      
      // Update tokens in localStorage
      tokenManager.saveTokens({
        access_token: data.access_token,
        refresh_token: data.refresh_token,
        expires_at: data.expires_at,
      });
      
      return data;
    },
  });
}

// Logout function (not a mutation since it's mostly client-side)
export function useLogout() {
  const queryClient = useQueryClient();
  
  return () => {
    // Make logout API call if server needs notification
    apiClient.post('/auth/logout', {}, { skipErrorToast: true })
      .catch(() => {
        // Ignore errors on logout
        console.log('Logout API call failed, but continuing with client-side logout');
      })
      .finally(() => {
        // Always clear tokens regardless of API success/failure
        tokenManager.clearTokens();
        
        // Invalidate and reset relevant queries
        queryClient.invalidateQueries({ queryKey: ["user"] });
        queryClient.resetQueries();
      });
  };
}

// Helper function to get stored auth data
export function getAuthData() {
  return tokenManager.getTokens();
}

// Helper to check if the user is authenticated
export function isAuthenticated() {
  return tokenManager.isAuthenticated();
} 