import { useMutation, useQuery } from "@tanstack/react-query";
import apiClient from "@/lib/api-client";
import { useQueryClient } from "@tanstack/react-query";
// import  task from "@/data.json"

// Custom error class for no tasks
export class NoTasksError extends Error {
  response: { status: number; data: { error: string } };
  
  constructor(message: string, status = 404) {
    super(message);
    this.name = 'NoTasksError';
    this.response = {
      status,
      data: { error: message }
    };
  }
}

// Types for Task API
export interface Message {
  id?: string;
  text: string;
  type: "sent" | "received";
  messageType: "image" | "text";
  timestamp: string;
  imageSrc?: string;
}

export interface ModeratorInfo {
  name: string
  username: string;
  gender: string;
  city: string;
  country: string;
  birthDate: {
    age: number;
    date: string;
  };
  profileText: string;
  hasPictures: boolean;
  hasProfilePic: boolean;
}

export interface CustomerInfo {
  username: string;
  gender: string;
  city: string;
  country: string;
  birthDate: {
    age: number;
    date: string;
  };
  profileText: string;
  hasPictures: boolean;
  hasProfilePic: boolean;
}

export interface Notes {
  rawText: string;
}

export interface RewriteConfig {
  attributes: string[];
  rewriteAge: number;
  rewriteGender: string;
  moderatorUsername: string;
}

export interface SiteInfos {
  origin: string;
  messages: Message[];
  accountId: string;
  metaData: {
    rewriteConfig: RewriteConfig;
    moderatorInfo: ModeratorInfo;
    customerInfo: CustomerInfo;
    moderatorNotes: Notes;
    customerNotes: Notes;
    moderatorUpdates: string[];
    customerUpdates: string[];
    ins: number;
    outs: number;
    chatId: number;
    customerProfilePic: string;
    moderatorProfilePic: string;
    minLength: number;
  };
}

export interface Task {
  taskId: string;
  data: {
    resText: string;
    siteInfos: SiteInfos;
  };
  expiresAt: string;
}

export interface TaskSubmission {
  taskId: string;
  expiresAt: string;
  data: Task['data'] & {
    language?: string;
  };
}

// React Query hook to fetch task (deprecated - use task store instead)
export function useTaskQuery(options = {}) {
  return useQuery({
    queryKey: ["task"],
    queryFn: async (): Promise<Task | null> => {
      try {
        const data = await apiClient.task.get<Task>("/task", { skipErrorToast: true });
        // const data = task as unknown as Task;
        console.log('Fetched task:', data);
        return data;
      } catch (error: unknown) {
        // Check if this is a 404 "No tasks available" error
        if (
          error &&
          typeof error === 'object' &&
          'response' in error &&
          error.response &&
          typeof error.response === 'object' &&
          'status' in error.response &&
          error.response.status === 404 &&
          'data' in error.response &&
          error.response.data &&
          typeof error.response.data === 'object' &&
          'error' in error.response.data &&
          error.response.data.error === "No tasks available"
        ) {
          // Convert to a NoTasksError
          throw new NoTasksError("No tasks available");
        }
        throw error;
      }
    },
    // Override default retry behavior to not retry for NoTasksError
    retry: (failureCount, error) => {
      if (error instanceof NoTasksError) {
        return false;
      }
      // Default retry behavior (retry 3 times for other errors)
      return failureCount < 3;
    },
    ...options,
  });
}


// React Query mutation to submit task (deprecated - use task store instead)
export function useSubmitTaskMutation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (submission: TaskSubmission): Promise<void> => {
      await apiClient.task.post("/task/submit", {
        ...submission,
        data: {
          ...submission.data,
          alert: ""
        }
      });
    },
    onSuccess: () => {
      queryClient.setQueryData(["task"], null);
      queryClient.invalidateQueries({ queryKey: ["task"] });
    },
  });
}