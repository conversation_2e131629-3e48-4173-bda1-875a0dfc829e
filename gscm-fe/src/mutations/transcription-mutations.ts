import { useMutation } from "@tanstack/react-query";
import apiClient from "@/lib/api-client";

/**
 * Transcription Mutations with Authentication
 *
 * Usage examples:
 *
 * // With authentication (default)
 * const mutation = useTranscriptionMutation();
 * mutation.mutate({ audio: audioBlob, language: "en" });
 *
 * // Without authentication
 * mutation.mutate({ audio: audioBlob, language: "en", requiresAuth: false });
 *
 * // Error handling version
 * const errorHandlingMutation = useTranscriptionWithErrorHandling();
 * const transcription = await errorHandlingMutation.mutateAsync({
 *   audio: audioBlob,
 *   language: "en",
 *   requiresAuth: true
 * });
 */

// Types for Transcription API
export interface TranscriptionRequest {
  audio: Blob;
  language: string;
  requiresAuth?: boolean; // Optional flag to control authentication
}

export interface TranscriptionResponse {
  success: boolean;
  transcription: string;
  error?: string;
}

// Base transcription API URL
const TRANSCRIPTION_API_URL = import.meta.env.VITE_TRANSCRIPTION_API_URL || 'http://localhost:8200';

/**
 * React Query mutation for audio transcription with authentication
 */
export function useTranscriptionMutation() {
  return useMutation({
    mutationFn: async ({ audio, language, requiresAuth = true }: TranscriptionRequest): Promise<TranscriptionResponse> => {
      try {
        // Create FormData to send the audio file
        const formData = new FormData();
        formData.append("audio", audio, "audio.webm");
        formData.append("language", language.toLowerCase());

        const data = await apiClient.postFormData<TranscriptionResponse>(
          "/transcribe/simple",
          formData,
          { requiresAuth },
          TRANSCRIPTION_API_URL
        );

        if (data.success && data.transcription) {
          return data;
        } else {
          throw new Error("No transcription received");
        }
      } catch (error) {
        console.error("Transcription error:", error);
        throw new Error(
          error instanceof Error
            ? error.message
            : "Failed to transcribe audio"
        );
      }
    },
  });
}

/**
 * Hook for transcription with custom error handling and authentication
 */
export function useTranscriptionWithErrorHandling() {
  return useMutation({
    mutationFn: async ({ audio, language, requiresAuth = true }: TranscriptionRequest): Promise<string> => {
      const formData = new FormData();
      formData.append("audio", audio, "audio.webm");
      formData.append("language", language.toLowerCase());

      const data = await apiClient.postFormData<TranscriptionResponse>(
        "/transcribe/simple",
        formData,
        { requiresAuth },
        TRANSCRIPTION_API_URL
      );

      if (data.success && data.transcription) {
        return data.transcription;
      } else {
        throw new Error("No transcription received");
      }
    },
  });
}
