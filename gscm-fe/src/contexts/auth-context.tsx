import { ReactNode, createContext, useContext, useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'sonner';
import tokenManager from '@/lib/token-manager';
import { useLoginMutation, useRegisterMutation, useRefreshTokenMutation, useLogout } from '@/mutations/auth-mutations';

interface User {
  id: string;
  email: string;
}

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (email: string, password: string) => Promise<void>;
  register: (username: string, password: string) => Promise<void>;
  logout: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const navigate = useNavigate();
  
  // Use React Query mutations
  const loginMutation = useLoginMutation();
  const registerMutation = useRegisterMutation();
  const refreshTokenMutation = useRefreshTokenMutation();
  const logoutFn = useLogout();
  
  // Check for existing session on mount
  useEffect(() => {
    const initAuth = async () => {
      try {
        setIsLoading(true);
        
        if (tokenManager.isAuthenticated()) {
          // Valid session exists
          // You could fetch user data here if needed
          const userData = JSON.parse(localStorage.getItem('user') || '{}');
          setUser(userData.id ? userData : null);
        } else {
          // Session expired, try to refresh
          const tokens = tokenManager.getTokens();
          if (tokens?.refresh_token) {
            try {
              const result = await refreshTokenMutation.mutateAsync({ 
                refresh_token: tokens.refresh_token 
              });
              
              setUser(result.user);
              localStorage.setItem('user', JSON.stringify(result.user));
            } catch (error) {
              console.error('Token refresh failed:', error);
              tokenManager.clearTokens();
            }
          }
        }
      } catch (error) {
        console.error('Auth initialization error:', error);
        // Clear any invalid auth data
        tokenManager.clearTokens();
      } finally {
        setIsLoading(false);
      }
    };

    initAuth();
  }, []);

  const login = async (email: string, password: string) => {
    try {
      setIsLoading(true);
      
      // Use React Query mutation instead of direct API call
      const result = await loginMutation.mutateAsync({ email, password });
      
      setUser(result.user);
      localStorage.setItem('user', JSON.stringify(result.user));
      
      toast.success('Logged in successfully');
      
      // Check if there's a saved redirect path
      const redirectPath = sessionStorage.getItem('redirectPath');
      if (redirectPath) {
        sessionStorage.removeItem('redirectPath');
        navigate(redirectPath);
      } else {
        // Default redirect to dashboard
        navigate('/dashboard');
      }
    } catch (error) {
      // Extract the error message from the API response
      let errorMessage = 'Failed to login';
      
      if (error instanceof Error) {
        // Try to parse the error message which might contain JSON
        try {
          const parsedError = JSON.parse(error.message);
          errorMessage = parsedError.error || error.message;
        } catch {
          // If not JSON, use the error message as is
          errorMessage = error.message;
        }
      }
      
      toast.error('Login failed', { description: errorMessage });
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (username: string, password: string) => {
    try {
      setIsLoading(true);
      
      // Use React Query mutation instead of direct API call
      await registerMutation.mutateAsync({ username, password });
      
      toast.success('Registration successful', { 
        description: 'You can now log in with your credentials' 
      });
      
      // Redirect to login page
      navigate('/login');
    } catch (error) {
      // Extract the error message from the API response
      let errorMessage = 'Failed to register';
      
      if (error instanceof Error) {
        // Try to parse the error message which might contain JSON
        try {
          const parsedError = JSON.parse(error.message);
          errorMessage = parsedError.error || error.message;
        } catch {
          // If not JSON, use the error message as is
          errorMessage = error.message;
        }
      }
      
      toast.error('Registration failed', { description: errorMessage });
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    setIsLoading(true);
    
    try {
      // Use the React Query logout function
      logoutFn();
      
      setUser(null);
      toast.success('Logged out successfully');
      navigate('/login');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        isLoading,
        isAuthenticated: tokenManager.isAuthenticated(),
        login,
        register,
        logout
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
} 