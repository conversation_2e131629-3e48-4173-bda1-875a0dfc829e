// Test utility to verify time tracking functionality
// This can be called from browser console to test the time tracking system

import { useTimeTrackingStore } from '@/stores/time-tracking-store';

export const testTimeTracking = () => {
  const store = useTimeTrackingStore.getState();
  
  console.log('🧪 Testing Time Tracking System');
  console.log('================================');
  
  // Test 1: Start timing
  const testTaskId = 'test-task-123';
  const testUserEmail = '<EMAIL>';
  const testExpiresAt = new Date(Date.now() + 180000).toISOString(); // 3 minutes from now
  
  console.log('1. Starting timing for test task...');
  store.startTiming(testTaskId, testExpiresAt, testUserEmail);
  
  // Test 2: Check current timing
  setTimeout(() => {
    console.log('2. Checking current timing after 2 seconds...');
    console.log('   Time provided:', store.getCurrentTimeProvided(), 'seconds');
    console.log('   Time taken so far:', store.getCurrentTimeTaken(), 'seconds');
    console.log('   Is timing active:', store.isTimingActive());
    
    // Test 3: Record completion
    setTimeout(() => {
      console.log('3. Recording completion after 5 seconds total...');
      const completion = store.recordCompletion('This is a test response');
      
      if (completion) {
        console.log('   ✅ Completion recorded:', {
          taskId: completion.taskId,
          userEmail: completion.userEmail,
          timeProvided: completion.timeProvided,
          timeTaken: completion.timeTaken,
          efficiency: `${Math.round((completion.timeTaken / completion.timeProvided) * 100)}%`,
          response: completion.response.substring(0, 50) + '...'
        });
      } else {
        console.log('   ❌ Failed to record completion');
      }
      
      // Test 4: Check stored completions
      console.log('4. Checking stored completions...');
      const completions = store.getCompletions();
      console.log('   Total completions:', completions.length);
      console.log('   User completions:', store.getCompletionsByUser(testUserEmail).length);
      
      // Test 5: Check localStorage
      console.log('5. Checking localStorage...');
      const storedData = localStorage.getItem('time-tracking-storage');
      if (storedData) {
        const parsed = JSON.parse(storedData);
        console.log('   ✅ Data persisted in localStorage:', parsed.state.completions.length, 'completions');
      } else {
        console.log('   ❌ No data found in localStorage');
      }
      
      console.log('🧪 Time tracking test completed!');
      
    }, 3000); // Wait 3 more seconds before recording completion
    
  }, 2000); // Wait 2 seconds before checking timing
};

// Export for browser console usage
if (typeof window !== 'undefined') {
  (window as any).testTimeTracking = testTimeTracking;
}

export const logTimeTrackingData = () => {
  const store = useTimeTrackingStore.getState();
  const completions = store.getCompletions();
  
  console.log('📊 Time Tracking Data Summary');
  console.log('=============================');
  console.log('Total completions:', completions.length);
  
  if (completions.length > 0) {
    console.log('Recent completions:');
    completions.slice(-5).forEach((completion, index) => {
      console.log(`${index + 1}. Task ${completion.taskId.slice(-8)} by ${completion.userEmail}`);
      console.log(`   Time: ${completion.timeTaken}s / ${completion.timeProvided}s (${Math.round((completion.timeTaken / completion.timeProvided) * 100)}% efficiency)`);
      console.log(`   Response: "${completion.response.substring(0, 50)}..."`);
      console.log(`   Completed: ${new Date(completion.completedAt).toLocaleString()}`);
    });
  }
  
  console.log('Current session active:', store.isTimingActive());
  if (store.isTimingActive()) {
    console.log('Current time taken:', store.getCurrentTimeTaken(), 'seconds');
    console.log('Current time provided:', store.getCurrentTimeProvided(), 'seconds');
  }
};

// Export for browser console usage
if (typeof window !== 'undefined') {
  (window as any).logTimeTrackingData = logTimeTrackingData;
}
